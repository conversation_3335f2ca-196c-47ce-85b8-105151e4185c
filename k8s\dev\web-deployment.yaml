﻿apiVersion: apps/v1
kind: Deployment
metadata:
  name: imip-wisma-web
  namespace: imip-wisma-dev-new
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0 # Ensures no pods are unavailable during the update
      maxSurge: 1 # Allows creating at most 1 extra pod during the update
  selector:
    matchLabels:
      app: imip-wisma-web
  template:
    metadata:
      labels:
        app: imip-wisma-web
    spec:
      nodeSelector:
        kubernetes.io/hostname: k8s-worker1
      # Add host aliases to resolve DNS names to specific IP addresses
      hostAliases:
        - ip: "**********"
          hostnames:
            - "api-identity-dev.imip.co.id"
            - "api-identity.imip.co.id"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - imip-wisma-web
                topologyKey: kubernetes.io/hostname
      terminationGracePeriodSeconds: 60
      volumes:
        - name: certificate-volume
          secret:
            secretName: imip-wisma-certificate
            items:
              - key: identity-server.pfx
                path: identity-server.pfx
              # Add a symlink for openiddict.pfx to identity-server.pfx
              - key: identity-server.pfx
                path: openiddict.pfx
        - name: data-protection-keys
          hostPath:
            path: /mnt/data/imip-wisma-data-protection
            type: DirectoryOrCreate
      initContainers:
        - name: init-data-protection
          image: busybox
          command:
            [
              "sh",
              "-c",
              "mkdir -p /app/data-protection-keys && chmod -R 777 /app/data-protection-keys && echo 'Data protection directory created' > /app/data-protection-keys/init.txt",
            ]
          volumeMounts:
            - name: data-protection-keys
              mountPath: /app/data-protection-keys
      imagePullSecrets:
        - name: gitlab-registry-credentials
      containers:
        - name: imip-wisma-web
          image: ${CI_REGISTRY_IMAGE}/web:${CI_COMMIT_SHA}
          # Remove command override to use Dockerfile ENTRYPOINT
          ports:
            - containerPort: 80
          volumeMounts:
            - name: certificate-volume
              mountPath: /app/certs
              readOnly: true
            - name: data-protection-keys
              mountPath: /app/data-protection-keys
          envFrom:
            - configMapRef:
                name: imip-wisma-config
            - secretRef:
                name: imip-wisma-secrets
          env:
            - name: TZ
              value: "Asia/Makassar"
            - name: ALLOW_DEV_CERTIFICATES
              value: "true"
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: SYNCFUSION_DEBUG
              value: "true"
            - name: SKIASHARP_DEBUG
              value: "true"
            - name: DOTNET_gcServer
              value: "1"
            - name: DOTNET_gcConcurrent
              value: "1"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          securityContext:
            runAsUser: 0
            allowPrivilegeEscalation: true
          resources:
            requests:
              memory: "768Mi"
              cpu: "500m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
          startupProbe:
            httpGet:
              path: /api/health/kubernetes
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 15
            failureThreshold: 15
          readinessProbe:
            httpGet:
              path: /api/health/kubernetes
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 15
            successThreshold: 1
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /api/health/kubernetes
              port: 80
            initialDelaySeconds: 90
            periodSeconds: 30
            timeoutSeconds: 15
            successThreshold: 1
            failureThreshold: 3
